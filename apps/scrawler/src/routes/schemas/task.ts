import { BatchAudienceMode } from '@/enums/TaskMode'
import { ProjectKolAttitude, SimilarChannelTaskStatus, TaskReason, TaskType } from '@repo/database'
import { Static, Type } from '@sinclair/typebox'

// 任务ID参数schema
export const TaskIdParamsSchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
})

export type TaskIdParams = Static<typeof TaskIdParamsSchema>

// 未终结任务查询参数schema
export const UnterminatedTasksQuerySchema = Type.Object({
  projectId: Type.String({ description: '项目ID' }),
  taskType: Type.Enum(TaskType, { description: '任务类型' }),
})

export type UnterminatedTasksQuery = Static<typeof UnterminatedTasksQuerySchema>

export const UserUnterminatedTasksQuerySchema = Type.Object({
  taskType: Type.Enum(TaskType, { description: '任务类型' }),
})

export type UserUnterminatedTasksQuery = Static<typeof UserUnterminatedTasksQuerySchema>

// 用户未终结任务响应 Schema（不包含 candidate 字段）
export const UserUnterminatedTaskResponseSchema = Type.Object({
  id: Type.String({ description: '任务ID' }),
  projectId: Type.String({ description: '项目ID' }),
  status: Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' }),
  params: Type.Any({ description: '任务参数' }),
  result: Type.Union([Type.Any(), Type.Null()], { description: '任务结果' }),
  errors: Type.Union([Type.Any(), Type.Null()], { description: '错误信息' }),
  meta: Type.Union([Type.Any(), Type.Null()], { description: '元数据' }),
  createdBy: Type.Union([Type.String(), Type.Null()], { description: '创建者ID' }),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  isTerminated: Type.Boolean({ description: '是否已终止' }),
  reason: Type.Enum(TaskReason, { description: '任务原因' }),
  type: Type.Enum(TaskType, { description: '任务类型' }),
  strategyId: Type.Union([Type.String(), Type.Null()], { description: '策略ID' }),
})

export type UserUnterminatedTaskResponse = Static<typeof UserUnterminatedTaskResponseSchema>

// 用户未终结任务列表响应 Schema
export const UserUnterminatedTasksResponseSchema = Type.Array(UserUnterminatedTaskResponseSchema, {
  description: '用户未终结任务列表',
})

export type UserUnterminatedTasksResponse = Static<typeof UserUnterminatedTasksResponseSchema>

// 任务操作类型枚举
export enum TaskActionType {
  PAUSE = 'pause',
  RESUME = 'resume',
  COMPLETE = 'complete',
}

// 任务操作请求schema
export const TaskActionRequestSchema = Type.Object({
  action: Type.Enum(TaskActionType, {
    description: '操作类型：pause(暂停)、resume(恢复)、complete(完成)',
  }),
  reason: Type.Optional(Type.String({ description: '操作原因，可选' })),
})

export type TaskActionRequest = Static<typeof TaskActionRequestSchema>

// 队列名称枚举
export enum QueueName {
  YOUTUBE = 'youtube-task-queue',
  TIKTOK = 'tiktok-task-queue',
  INSTAGRAM = 'instagram-task-queue',
  EASYKOL_TRACK = 'easykol-track-queue',
  LONG_CRAWLER = 'long-crawler-task-queue',
}

// 队列任务状态枚举
export enum QueueJobStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  FAILED = 'failed',
  DELAYED = 'delayed',
  WAITING = 'waiting',
}

// 获取队列任务请求schema
export const QueueTasksQuerySchema = Type.Object({
  queueName: Type.Enum(QueueName, { description: '队列名称' }),
  status: Type.Optional(
    Type.Enum(QueueJobStatus, {
      description:
        '任务状态：active(活动中), completed(已完成), failed(已失败), delayed(延迟中), waiting(等待中)',
    }),
  ),
})

export type QueueTasksQuery = Static<typeof QueueTasksQuerySchema>

// 失败所有队列任务请求schema
export const FailAllQueueTasksSchema = Type.Object({
  queueName: Type.Enum(QueueName, { description: '队列名称' }),
  reason: Type.Optional(Type.String({ description: '失败原因，可选' })),
})

export type FailAllQueueTasksRequest = Static<typeof FailAllQueueTasksSchema>

// 失败特定队列任务请求schema（单个任务）
export const FailQueueTaskByIdParamsSchema = Type.Object({
  queueName: Type.String({ description: '队列名称' }),
  jobId: Type.String({ description: '任务ID' }),
})

export const FailQueueTaskByIdBodySchema = Type.Object({
  reason: Type.Optional(Type.String({ description: '失败原因，可选' })),
})

export type FailQueueTaskByIdParams = Static<typeof FailQueueTaskByIdParamsSchema>
export type FailQueueTaskByIdBody = Static<typeof FailQueueTaskByIdBodySchema>

// 批量失败队列任务请求schema
export const BatchFailQueueTasksSchema = Type.Object({
  queueName: Type.Enum(QueueName, { description: '队列名称' }),
  jobIds: Type.Array(Type.String(), { description: '任务ID列表' }),
  reason: Type.Optional(Type.String({ description: '失败原因，可选' })),
})

export type BatchFailQueueTasksRequest = Static<typeof BatchFailQueueTasksSchema>

// 任务列表查询参数schema
export const TaskListQuerySchema = Type.Object({
  email: Type.Optional(Type.String({ description: '用户邮箱' })),
  projectId: Type.Optional(Type.String({ description: '项目ID' })),
  platform: Type.Optional(Type.String({ description: '平台类型' })),
  status: Type.Optional(Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' })),
  taskType: Type.Optional(Type.Enum(TaskType, { description: '任务类型' })),
  taskReason: Type.Optional(Type.Enum(TaskReason, { description: '任务原因' })),
  page: Type.Optional(Type.String({ description: '页码', default: '1' })),
  pageSize: Type.Optional(Type.String({ description: '每页数量', default: '10' })),
  isZeroSimilarTask: Type.Optional(
    Type.Boolean({ description: '是否为0相似任务', default: false }),
  ),
})

export type TaskListQuery = Static<typeof TaskListQuerySchema>

// ProjectKol 统计查询参数 schema
export const ProjectKolStatsQuerySchema = Type.Object({
  taskId: Type.String({ description: '任务ID' }),
  attitude: Type.Optional(
    Type.Enum(ProjectKolAttitude, {
      description: '态度筛选：LIKE(喜欢)、DISLIKE(不喜欢)、NORATE(未评价)、SUPERLIKE(超级喜欢)'
    })
  ),
  userId: Type.Optional(
    Type.String({
      description: '要查询的用户ID，不传则查询当前用户'
    })
  ),
})

export type ProjectKolStatsQuery = Static<typeof ProjectKolStatsQuerySchema>

// ProjectKol 统计响应 schema
export const ProjectKolStatsResponseSchema = Type.Object({
  totalCount: Type.Number({ description: '总数量' }),
  likeCount: Type.Number({ description: 'LIKE 数量' }),
  dislikeCount: Type.Number({ description: 'DISLIKE 数量' }),
  norateCount: Type.Number({ description: 'NORATE 数量' }),
  superlikeCount: Type.Number({ description: 'SUPERLIKE 数量' }),
  breakdown: Type.Object({
    LIKE: Type.Number({ description: 'LIKE 数量' }),
    DISLIKE: Type.Number({ description: 'DISLIKE 数量' }),
    NORATE: Type.Number({ description: 'NORATE 数量' }),
    SUPERLIKE: Type.Number({ description: 'SUPERLIKE 数量' }),
  }, { description: '按态度分组的详细统计' }),
})

export type ProjectKolStatsResponse = Static<typeof ProjectKolStatsResponseSchema>

// 批量受众分析请求schema
export const BatchAudienceRequestSchema = Type.Object({
  links: Type.Array(Type.String(), {
    description: '链接列表，支持TikTok、Instagram、YouTube的用户或帖子链接',
    minItems: 1,
    maxItems: 50,
  }),
  mode: Type.Enum(BatchAudienceMode, {
    description: '分析模式：1-用户受众分析，2-帖子受众分析',
  }),
})

export type BatchAudienceRequest = Static<typeof BatchAudienceRequestSchema>

// 批量任务ID请求schema
export const BatchTaskIdsRequestSchema = Type.Object({
  taskIds: Type.Array(Type.String(), {
    description: '任务ID列表',
    minItems: 1,
    maxItems: 50,
  }),
})

export type BatchTaskIdsRequest = Static<typeof BatchTaskIdsRequestSchema>

// 批次任务列表查询参数schema
export const BatchTaskListQuerySchema = Type.Object({
  page: Type.Optional(Type.Number({ description: '页码', default: 1, minimum: 1 })),
  pageSize: Type.Optional(
    Type.Number({ description: '每页数量', default: 20, minimum: 1, maximum: 100 }),
  ),
  taskType: Type.Optional(Type.Enum(TaskType, { description: '任务类型' })),
})

export type BatchTaskListQuery = Static<typeof BatchTaskListQuerySchema>

// 批次任务详情参数schema
export const BatchTaskDetailParamsSchema = Type.Object({
  batchId: Type.String({ description: '批次ID' }),
})

export type BatchTaskDetailParams = Static<typeof BatchTaskDetailParamsSchema>

// 批次任务导出参数schema
export const BatchTaskExportParamsSchema = Type.Object({
  batchId: Type.String({ description: '批次ID' }),
})

export type BatchTaskExportParams = Static<typeof BatchTaskExportParamsSchema>

// 批次任务列表查询参数
export interface BatchTaskListQueryParams {
  page?: number
  pageSize?: number
  taskType?: TaskType
}

// 批次任务列表响应
export interface BatchTaskListResponse {
  total: number
  page: number
  pageSize: number
  totalPages: number
  data: BatchTaskWithProgress[]
}

// 带进度的批次任务
export interface BatchTaskWithProgress {
  id: string
  userId: string
  taskType: TaskType
  rawInput: any
  result: any
  summary: any
  tasks: string[]
  createdAt: Date
  updatedAt: Date
  progress: number
  statusCounts: {
    completed: number
    failed: number
    processing: number
    pending: number
  }
  totalTasks: number
}

// 批次任务详情响应
export interface BatchTaskDetailResponse {
  id: string
  userId: string
  taskType: TaskType
  rawInput: any
  result: any
  summary: any
  tasks: string[]
  createdAt: Date
  updatedAt: Date
  progress: number
  statusCounts: {
    completed: number
    failed: number
    processing: number
    pending: number
  }
  totalTasks: number
}

// 响应 Schema 定义
// 批量创建受众分析任务响应 Schema
export const BatchAudienceTasksResponseSchema = Type.Object({
  batchId: Type.String({ description: '批次记录ID' }),
  results: Type.Array(
    Type.Object({
      taskId: Type.String(),
      link: Type.String(),
      status: Type.Union([Type.Literal('created'), Type.Literal('failed')]),
      error: Type.Optional(Type.String()),
      delay: Type.Optional(Type.Number()),
      batchIndex: Type.Optional(Type.Number()),
      username: Type.Optional(Type.String()),
      videoId: Type.Optional(Type.String()),
    }),
  ),
  summary: Type.Object({
    total: Type.Number(),
    success: Type.Number(),
    failed: Type.Number(),
    batchSize: Type.Number(),
    totalBatches: Type.Number(),
    delayBetweenBatches: Type.Number(),
    mode: Type.Enum(BatchAudienceMode),
  }),
})

// 批次任务列表响应 Schema
export const BatchTaskListResponseSchema = Type.Object({
  total: Type.Number(),
  page: Type.Number(),
  pageSize: Type.Number(),
  totalPages: Type.Number(),
  data: Type.Array(
    Type.Object({
      id: Type.String(),
      userId: Type.String(),
      taskType: Type.Enum(TaskType),
      rawInput: Type.Any(),
      result: Type.Any(),
      summary: Type.Any(),
      tasks: Type.Array(Type.String()),
      createdAt: Type.String(),
      updatedAt: Type.String(),
      progress: Type.Number(),
      statusCounts: Type.Object({
        completed: Type.Number(),
        failed: Type.Number(),
        processing: Type.Number(),
        pending: Type.Number(),
      }),
      totalTasks: Type.Number(),
    }),
  ),
})

// 批次任务详情响应 Schema
export const BatchTaskDetailResponseSchema = Type.Object({
  id: Type.String(),
  userId: Type.String(),
  taskType: Type.Enum(TaskType),
  rawInput: Type.Any(),
  result: Type.Any(),
  summary: Type.Any(),
  tasks: Type.Array(Type.String()),
  createdAt: Type.String(),
  updatedAt: Type.String(),
  progress: Type.Number(),
  statusCounts: Type.Object({
    completed: Type.Number(),
    failed: Type.Number(),
    processing: Type.Number(),
    pending: Type.Number(),
  }),
  totalTasks: Type.Number(),
})

// 批次任务导出响应 Schema
export const BatchTaskExportResponseSchema = Type.String({ description: 'Excel文件下载URL' })

// 受众分析历史导出响应 Schema
export const AudienceTasksExportResponseSchema = Type.String({ description: 'Excel文件下载URL' })

// 任务详情响应 Schema
export const TaskDetailResponseSchema = Type.Object({
  id: Type.String({ description: '任务ID' }),
  projectId: Type.String({ description: '项目ID' }),
  status: Type.Enum(SimilarChannelTaskStatus, { description: '任务状态' }),
  params: Type.Any({ description: '任务参数' }),
  result: Type.Optional(Type.Any({ description: '任务结果' })),
  errors: Type.Optional(Type.Any({ description: '错误信息' })),
  meta: Type.Optional(Type.Any({ description: '元数据' })),
  createdBy: Type.Optional(Type.String({ description: '创建者ID' })),
  createdAt: Type.String({ description: '创建时间', format: 'date-time' }),
  updatedAt: Type.String({ description: '更新时间', format: 'date-time' }),
  isTerminated: Type.Boolean({ description: '是否已终止' }),
  reason: Type.Enum(TaskReason, { description: '任务原因' }),
  type: Type.Enum(TaskType, { description: '任务类型' }),
  strategyId: Type.Optional(Type.String({ description: '策略ID' })),
  candidate: Type.Optional(Type.Any({ description: '候选数据' })),
})

export type TaskDetailResponse = Static<typeof TaskDetailResponseSchema>
